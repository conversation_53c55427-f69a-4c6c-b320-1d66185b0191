import React, { useState, useEffect } from 'react';
import { useCreateInterventionFromSettings } from '../../services/queries/interventionQueries';
import { useLocation, useNavigate, matchPath } from 'react-router-dom';
import { gameService, Game } from '../../services/gameService';
import { testService } from '../../services/testService';
import { Test } from '../../types/test';
import gameImage from '../../assets/game1.png';
import testAssignmentService, {
  TestAssignmentSettings,
} from '../../services/testAssignmentService';
import { authService } from '../../services/authService';
// import interventionService from '../../services/interventionService';
import { GameCard } from '../../components/games/GameCard';
import { GameSettingsModal } from '../modals/GameSettingsModal';
import type { GameSettings } from '../modals/GameSettingsModal';
import { InterventionFilters } from '../interventions/InterventionFilters';
import { useInterventionFilterStore } from '../../store/interventionFilterStore.ts';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
  DrawerClose,
} from '../../games/components/ui/drawer';
import { useStudentsStore } from '../../store/studentsStore';
import { toast } from 'sonner';

export const GamesPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  // const queryClient = useQueryClient();
  const createInterventionMutation = useCreateInterventionFromSettings();

  // Detect if we are inside student profile route
  const matchStudent =
    matchPath('/dashboard/student/:studentId/*', location.pathname) ||
    matchPath('/dashboard/student/:studentId', location.pathname);
  const studentRouteId = matchStudent?.params?.studentId;
  const isStudentProfileRoute = Boolean(studentRouteId);

  // Determine if current route is Tests tab (catalog) or student tests
  const isTestsPage = location.pathname.startsWith('/dashboard/tests') || Boolean(studentRouteId);
  const [games, setGames] = useState<Game[]>([]);
  const [allGames, setAllGames] = useState<Game[]>([]);
  const [_loading, setLoading] = useState(true);
  const [studentId, setStudentId] = useState<string>('guest-user');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedGame, setSelectedGame] = useState<Game | null>(null);
  const [selectedStudents, setSelectedStudents] = useState<Array<{ id: number; name: string }>>([]);
  const [showStudentRequired, setShowStudentRequired] = useState(false);
  const [isStudentPopupOpen, setIsStudentPopupOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Get filter store
  const { filters, updateAvailableOptions, filterActivities } = useInterventionFilterStore();

  const {
    students = [],
    loading: studentsLoading,
    error: studentsError,
    fetchStudents,
  } = useStudentsStore();

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const userData = await authService.getCurrentUser();
        // Preferuj student.id, fallback do user_id
        const id = userData?.student?.id || userData?.user_id;
        if (id) {
          setStudentId(id.toString());
        }
      } catch (err) {
        // Error handling
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentUser();
    fetchStudents();
  }, [fetchStudents]);

  useEffect(() => {
    const fetchGames = async () => {
      try {
        if (isTestsPage && studentRouteId) {
          // show only tests assigned to that student
          const assigned = await testService.listAssignedTests(Number(studentRouteId));
          const gamesMapped = assigned.map((test: Test) => ({
            id: test.id,
            name: test.title,
            description: `Assessment with ${test.questions.length} questions`,
            thumbnail: gameImage,
            categories: ['Assessment'],
            difficulty_level: 'medium',
            target_age_group: '6-12',
          }));
          setAllGames(gamesMapped);
          setGames(gamesMapped);
        } else {
          const { games } = await gameService.getGames(isTestsPage);
          setAllGames(games);
          setGames(games);

          // Update available filter options for interventions
          if (!isTestsPage && games.length > 0) {
            // Convert games to activities format for filter store
            const activities = games.map(game => ({
              title: game.name,
              description: game.description,
              images: [game.thumbnail],
              numberOfExercises: 1,
              game_id: game.id,
              objective: game.description,
              target_age_group: game.target_age_group || '',
              difficulty_level: game.difficulty_level || '',
              categories: game.categories || [],
              main_category: game.main_category || '',
            }));
            updateAvailableOptions(activities);
          }
        }
      } catch (error) {
        // Error handling
      }
    };

    fetchGames();
  }, [isTestsPage, studentRouteId, updateAvailableOptions]);

  // Apply filters when filters change
  useEffect(() => {
    if (!isTestsPage && allGames.length > 0) {
      // Convert games to activities format for filtering
      const activities = allGames.map(game => ({
        title: game.name,
        description: game.description,
        images: [game.thumbnail],
        numberOfExercises: 1,
        game_id: game.id,
        objective: game.description,
        target_age_group: game.target_age_group || '',
        difficulty_level: game.difficulty_level || '',
        categories: game.categories || [],
        main_category: game.main_category || '',
      }));

      const filteredActivities = filterActivities(activities);

      // Convert back to games format
      const filteredGames = filteredActivities.map(activity => {
        const originalGame = allGames.find(game => game.id === activity.game_id);
        return (
          originalGame || {
            id: activity.game_id,
            name: activity.title,
            description: activity.description,
            thumbnail: activity.images[0] || gameImage,
            categories: activity.categories,
            difficulty_level: activity.difficulty_level,
            target_age_group: activity.target_age_group,
          }
        );
      });

      setGames(filteredGames);
    }
  }, [filters, allGames, isTestsPage, filterActivities]);

  const handleGameCardClick = (game: Game) => {
    setSelectedGame(game);
    setIsDrawerOpen(true);
  };

  // Resetowanie pola wyszukiwania przy otwarciu modalu
  useEffect(() => {
    if (isStudentPopupOpen) {
      setSearchQuery('');
    }
  }, [isStudentPopupOpen]);

  const handleAssignClick = () => {
    if (selectedStudents.length === 0) {
      toast.error('Please select at least one student');
      setShowStudentRequired(true);
      return;
    }
    setIsModalOpen(true);
    setIsDrawerOpen(false); // Close Drawer after clicking Assign button
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleSaveSettings = async (settings: GameSettings) => {
    try {
      // Save settings / assign
      if (isTestsPage && selectedGame) {
        if (selectedStudents.length === 0) {
          toast.error('Select at least one student');
          return;
        }
        const promises = selectedStudents.map(async s => {
          const testSettings: TestAssignmentSettings = {
            testId: selectedGame.id,
            testTitle: selectedGame.name,
            frequency: {
              startDate: settings.frequency.startDate || new Date().toISOString().split('T')[0],
              repeatEveryUnit:
                (settings.frequency.repeatEvery as 'day' | 'week' | 'month') || 'day',
              repeatEveryValue: 1,
              endType: settings.frequency.endType as 'never' | 'on' | 'after',
              endDate: settings.frequency.endDate,
              endTimes: settings.frequency.endTimes
                ? Number(settings.frequency.endTimes)
                : undefined,
            },
            studentId: s.id,
          };
          await testAssignmentService.createAndAssignTest(testSettings, s.id);
        });
        await Promise.all(promises);
        toast.success('Test assigned successfully');
      } else if (selectedGame) {
        if (selectedStudents.length === 0) {
          toast.error('Select at least one student');
          return;
        }
        // Przypisujemy interwencje sekwencyjnie, aby uniknąć podwójnych żądań
        for (const s of selectedStudents) {
          const interventionSettings = {
            gameType: selectedGame.id,
            gameTitle: selectedGame.name,
            difficultyLevel: settings.difficultyLevel || 'Medium',
            duration: settings.duration || 15,
            frequency: {
              repeatEvery: '1',
              periodType: 'Week',
              daysOfWeek: ['Monday', 'Wednesday', 'Friday'],
              timesPerWeek: 3,
              endType: 'never' as const,
            },
          };
          await createInterventionMutation.mutateAsync({
            settings: interventionSettings,
            studentId: s.id,
          });
        }

        toast.success('Intervention assigned successfully');
      }
    } catch (err) {
      // Error handling
      toast.error('Failed to assign test');
    } finally {
      setIsModalOpen(false);

      // Jeśli jesteśmy na stronie studenta, nie przekierowuj nigdzie
      // Jeśli nie, przekieruj do strony studenta, aby zobaczyć przypisane interwencje
      if (selectedStudents.length === 1 && isStudentProfileRoute) {
        // Pozostajemy na stronie studenta - dane zostaną odświeżone automatycznie
        toast.success('Interwencja przypisana. Dane zostaną odświeżone automatycznie.');
      } else if (selectedStudents.length === 1) {
        // Przekieruj do strony studenta
        navigate(`/dashboard/student/${selectedStudents[0].id}`);
      } else {
        // Jeśli wybrano wielu studentów, przejdź do strony interwencji/testów
        const basePath = isTestsPage ? 'tests' : 'interventions';
        navigate(`/dashboard/${basePath}`);
      }
    }
  };

  return (
    <div className="space-y-8 mt-8">
      <section>
        <h2 className="text-[32px] leading-[32px] tracking-[0%] font-[400] font-['Fira_Sans'] text-[#5E6C84] mb-8">
          {isTestsPage ? 'Tests' : 'Available Interventions'}
        </h2>

        {/* Show filters only for interventions page, not tests */}
        {!isTestsPage && (
          <div className="mb-8">
            <InterventionFilters />
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-8">
          {games.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <div className="text-gray-500 text-lg">
                {!isTestsPage &&
                (filters.searchQuery ||
                  filters.selectedCategories.length > 0 ||
                  filters.selectedDifficultyLevels.length > 0 ||
                  filters.selectedTargetAgeGroups.length > 0)
                  ? 'No interventions match your current filters.'
                  : 'No interventions available.'}
              </div>
            </div>
          ) : (
            games.map(game => (
              <GameCard
                key={game.id}
                game={game}
                studentId={studentId}
                onClick={() => handleGameCardClick(game)}
              />
            ))
          )}
        </div>
      </section>

      <Drawer open={isDrawerOpen} onOpenChange={setIsDrawerOpen} direction="right">
        <DrawerContent className="w-full md:w-[400px] h-full">
          <DrawerHeader>
            {selectedGame && (
              <>
                <div className="w-full mb-4">
                  <img
                    src={selectedGame.thumbnail}
                    alt={selectedGame.name}
                    className="w-full object-cover h-48 rounded-lg"
                  />
                </div>
                <DrawerTitle className="text-2xl font-bold">{selectedGame.name}</DrawerTitle>
                <DrawerDescription className="mt-2">{selectedGame.description}</DrawerDescription>
              </>
            )}
          </DrawerHeader>

          {selectedGame && (
            <div className="p-4">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Select Student</h3>
                {studentsLoading ? (
                  <div className="flex items-center justify-center p-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    <span className="ml-2">Loading student list...</span>
                  </div>
                ) : studentsError ? (
                  <div className="p-4 bg-red-50 rounded-md">
                    <p className="text-red-600 font-medium">Error loading student list</p>
                    <p className="text-red-500 text-sm mt-1">{studentsError}</p>
                    <button
                      onClick={fetchStudents}
                      className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                    >
                      Try again
                    </button>
                  </div>
                ) : !students || students.length === 0 ? (
                  <div className="p-4 bg-yellow-50 rounded-md">
                    <p className="text-yellow-700">No students available</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="space-y-2">
                      <button
                        type="button"
                        onClick={() => {
                          setSearchQuery('');
                          setIsStudentPopupOpen(true);
                        }}
                        className={`w-full p-3 border rounded-md flex items-center justify-between ${showStudentRequired && selectedStudents.length === 0 ? 'border-red-500' : 'border-gray-300'}`}
                      >
                        <span
                          className={
                            selectedStudents.length > 0 ? 'text-gray-900' : 'text-gray-500'
                          }
                        >
                          {selectedStudents.length > 0
                            ? `Selected ${selectedStudents.length} students`
                            : 'Select students'}
                        </span>
                        <svg
                          className="w-5 h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>

                      {selectedStudents.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {selectedStudents.map(student => (
                            <div
                              key={student.id}
                              className="flex items-center bg-[#006699] text-white rounded-full px-3 py-1 text-sm"
                            >
                              <span>{student.name}</span>
                              <button
                                onClick={() =>
                                  setSelectedStudents(prev => prev.filter(s => s.id !== student.id))
                                }
                                className="flex items-center ml-2 text-white hover:text-blue-800 focus:outline-none"
                              >
                                <svg
                                  className="w-4 h-4"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M6 18L18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {showStudentRequired && selectedStudents.length === 0 && (
                      <p className="text-sm text-red-500">Please select at least one student</p>
                    )}

                    {isStudentPopupOpen && (
                      <div
                        className="fixed inset-0 backdrop-blur-sm bg-white/30 flex items-center justify-center z-50 animate-fadeIn"
                        onClick={() => setIsStudentPopupOpen(false)}
                      >
                        <div
                          className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] m-4 flex flex-col overflow-hidden animate-scaleIn"
                          onClick={e => e.stopPropagation()}
                        >
                          <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                            <button
                              onClick={() => setIsStudentPopupOpen(false)}
                              className="text-gray-500 hover:text-gray-700 focus:outline-none"
                            >
                              <svg
                                className="w-6 h-6"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M6 18L18 6M6 6l12 12"
                                />
                              </svg>
                            </button>
                          </div>

                          <div className="px-6 py-4 border-b border-gray-200">
                            <h2 className="text-xl font-bold mb-4">Select a student:</h2>
                            <div className="relative">
                              <input
                                type="text"
                                placeholder="Search student"
                                value={searchQuery}
                                onChange={e => setSearchQuery(e.target.value)}
                                className="w-full py-2 px-4 pr-10 border border-[#e0e0e0] rounded-md border-dashed"
                                autoFocus
                              />
                              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <svg
                                  className="w-5 h-5 text-gray-500"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                  />
                                </svg>
                              </div>
                            </div>
                          </div>

                          <div className="flex-1 overflow-y-auto p-4">
                            {students.filter(student => {
                              const fullName =
                                `${student.first_name} ${student.last_name}`.toLowerCase();
                              return fullName.includes(searchQuery.toLowerCase());
                            }).length === 0 ? (
                              <div className="p-6 text-center text-gray-500">No students found</div>
                            ) : (
                              <ul className="space-y-2 px-2">
                                {students
                                  .filter(student => {
                                    const fullName =
                                      `${student.first_name} ${student.last_name}`.toLowerCase();
                                    return fullName.includes(searchQuery.toLowerCase());
                                  })
                                  .map(student => (
                                    <li key={student.id}>
                                      <button
                                        type="button"
                                        className={`cursor-pointer w-full text-left px-4 py-3 hover:bg-gray-100 transition-colors flex items-center justify-between border border-dashed border-[#e0e0e0] rounded-md bg-[#F9F9F9] ${selectedStudents.some(s => s.id === student.id) ? 'bg-blue-50' : ''}`}
                                        onClick={() => {
                                          const isSelected = selectedStudents.some(
                                            s => s.id === student.id
                                          );
                                          if (isSelected) {
                                            // Jeśli student jest już wybrany, usuń go z listy
                                            setSelectedStudents(prev =>
                                              prev.filter(s => s.id !== student.id)
                                            );
                                          } else {
                                            // Jeśli student nie jest wybrany, dodaj go do listy
                                            setSelectedStudents(prev => [
                                              ...prev,
                                              {
                                                id: student.id,
                                                name: `${student.first_name} ${student.last_name}`,
                                              },
                                            ]);
                                          }
                                          setShowStudentRequired(false);
                                        }}
                                      >
                                        <div className="flex items-center">
                                          <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0 border border-gray-200">
                                            <svg
                                              className="w-6 h-6 text-gray-400"
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth="1.5"
                                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                              ></path>
                                            </svg>
                                          </div>
                                          <div className="font-medium text-gray-900">
                                            {student.first_name} {student.last_name}
                                          </div>
                                        </div>
                                        {selectedStudents.some(s => s.id === student.id) ? (
                                          <svg
                                            className="w-6 h-6 text-[#006699]"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                          >
                                            <path
                                              strokeLinecap="round"
                                              strokeLinejoin="round"
                                              strokeWidth="2"
                                              d="M5 13l4 4L19 7"
                                            />
                                          </svg>
                                        ) : (
                                          <div className="w-6 h-6 rounded-full border border-[#006699] flex items-center justify-center">
                                            <svg
                                              className="w-4 h-4 text-[#006699]"
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                              />
                                            </svg>
                                          </div>
                                        )}
                                      </button>
                                    </li>
                                  ))}
                              </ul>
                            )}
                          </div>

                          <div className="p-4 border-t border-gray-200 flex justify-between">
                            <button
                              type="button"
                              className="flex-1 mr-2 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                              onClick={() => setIsStudentPopupOpen(false)}
                            >
                              Cancel
                            </button>
                            <button
                              type="button"
                              className="flex-1 ml-2 py-2 bg-[#006699] text-white rounded-md hover:bg-[#005588] transition-colors"
                              onClick={() => setIsStudentPopupOpen(false)}
                            >
                              Done ({selectedStudents.length})
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="mt-4 space-y-2">
                <div>
                  <p className="text-sm text-gray-600">Difficulty level:</p>
                  <p className="font-medium">{selectedGame.difficulty_level || 'Brak danych'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Estimated time:</p>
                  <p className="font-medium">~15 min</p>
                </div>
              </div>
            </div>
          )}
          <DrawerFooter>
            <button
              className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={studentsLoading}
              onClick={handleAssignClick}
            >
              Assign
            </button>
            <DrawerClose asChild>
              <button className="w-full py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                Close
              </button>
            </DrawerClose>
          </DrawerFooter>
        </DrawerContent>
      </Drawer>

      {selectedGame && selectedStudents.length > 0 && (
        <GameSettingsModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          onSave={handleSaveSettings}
          gameType={selectedGame.id}
          gameTitle={selectedGame.name}
          isTestsPage={isTestsPage}
          studentId={selectedStudents.length > 0 ? selectedStudents[0].id : undefined}
        />
      )}
    </div>
  );
};
