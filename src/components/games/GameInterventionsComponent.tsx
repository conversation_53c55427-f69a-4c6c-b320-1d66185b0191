import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useLocation } from 'react-router-dom';
import GameSettingsModal from '../modals/GameSettingsModal';
import SuccessModal from '../modals/SuccessModal';
import interventionService, { getFallbackGamesForArea } from '../../services/interventionService';

// import { useCurrentStudentId } from '../../hooks/useCurrentStudentId'; // We no longer use the hook to get student ID
import logger from '../../utils/logger';

import { GameType } from '../../services/gameService';

type InterventionSettingsType = import('../../services/interventionService').InterventionSettings;

interface GameSettings {
  difficultyLevel: string;
  frequency: {
    repeatEvery: string;
    periodType: string;
    timesPerWeek?: number | string;
    daysOfWeek?: string[];
    startDate?: string;
    endType?: 'never' | 'on' | 'after';
    endTimes?: string | number;
    endDate?: string;
  };
  duration: number;
  details?: Record<string, unknown>;
  promptAi?: {
    customPrompt: string;
  };
}

import game1Image from '../../assets/game1.png';
import game2Image from '../../assets/game2.png';
import game3Image from '../../assets/game3.png';

const GAME_TYPES = {
  BLEND: 'blend',
  MATH: 'math',
  MEMORY: 'memory',
};

interface Game {
  id: number;
  type: string;
  title: string;
  description: string;
  exerciseTime: string;
  imageUrl: string;
  game_id?: string; // Optional game_id for intervention games
}

interface GameInterventionsComponentProps {
  areaId: string;
  areaName: string;
  studentId?: number;
  gradeLevel?: string;
  predefinedGames?: Game[]; // Optional prop for predefined games
}

const GameInterventionsComponent: React.FC<GameInterventionsComponentProps> = ({
  areaId,
  areaName,
  studentId,
  gradeLevel,
  predefinedGames,
}) => {
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false);
  const [activeGame, setActiveGame] = useState<Game | null>(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  // Get current student ID using the custom hook
  const currentStudentId = studentId; // We only use the ID passed via props

  useEffect(() => {
    const fetchGames = async () => {
      try {
        setLoading(true);

        // If predefined games are provided, use them
        if (predefinedGames && predefinedGames.length > 0) {
          setGames(predefinedGames);
          setLoading(false);
          return;
        }

        let gamesForArea = getFallbackGamesForArea(areaId);

        if (gradeLevel) {
          logger.log(`Filtering games for grade level: ${gradeLevel}`);
        }

        if (gamesForArea.length === 0) {
          logger.log('No games found for area:', areaId);
          logger.log('Adding default games');

          gamesForArea = [
            {
              id: 1000 + Math.floor(Math.random() * 1000),
              type: GAME_TYPES.BLEND,
              title: `${areaName} Activity`,
              description: `Practice ${areaName.toLowerCase()} skills with this interactive game.`,
              exerciseTime: '5 min.',
              imageUrl: game1Image,
            },
            {
              id: 2000 + Math.floor(Math.random() * 1000),
              type: GAME_TYPES.MATH,
              title: `${areaName} Quiz`,
              description: `Improve ${areaName.toLowerCase()} skills with this quiz.`,
              exerciseTime: '8 min.',
              imageUrl: game2Image,
            },
            {
              id: 3000 + Math.floor(Math.random() * 1000),
              type: GAME_TYPES.MEMORY,
              title: `${areaName} Memory Game`,
              description: `Enhance ${areaName.toLowerCase()} skills with this memory game.`,
              exerciseTime: '10 min.',
              imageUrl: game3Image,
            },
          ];
        }

        setGames(gamesForArea);
      } catch (error) {
        logger.error('Error fetching games:', error);
        toast.error('Failed to load games for selected area');
      } finally {
        setLoading(false);
      }
    };

    fetchGames();
  }, [areaId, areaName, gradeLevel, predefinedGames]);

  // Get location to extract student ID from URL
  const location = useLocation();

  // Function to extract student ID from URL
  const getStudentIdFromUrl = (): number | null => {
    // Example URL format: /dashboard/student/229/chat
    const match = location.pathname.match(/\/student\/([0-9]+)/);
    if (match && match[1]) {
      return parseInt(match[1], 10);
    }
    return null;
  };

  const openGameSettings = (game: Game) => {
    // Simply open the settings modal - API call will be made when user clicks "Add intervention"
    // First check if student ID was passed as prop, if not, try to get it from URL
    const effectiveStudentId = currentStudentId || getStudentIdFromUrl();

    if (!effectiveStudentId) {
      logger.error('No student ID available');
      toast.error('Cannot open settings: Student ID not found');
      return;
    }

    logger.log('Opening settings modal for game:', game.title);
    setActiveGame(game);
    setIsSettingsModalOpen(true);
  };

  const handleSaveSettings = async (settings: GameSettings) => {
    const timestamp = new Date().toISOString();
    logger.log(`[${timestamp}] handleSaveSettings called with settings:`, settings);
    if (!activeGame) return;

    // Prevent double execution
    if (isProcessing) {
      logger.log(`[${timestamp}] Assignment already in progress, skipping duplicate call`);
      return;
    }

    // Use the real student ID - first check if student ID was passed as prop, if not, try to get it from URL
    const effectiveStudentId = currentStudentId || getStudentIdFromUrl();

    if (!effectiveStudentId) {
      logger.error('No student ID available for saving settings');
      toast.error('Cannot save settings: Student ID not found');
      return;
    }

    setIsProcessing(true);
    try {
      // Use game_id if available (from intervention data), otherwise map game type to intervention_id
      let interventionId: string;

      if (activeGame.game_id) {
        // Use the game_id directly from intervention data
        interventionId = activeGame.game_id;
        logger.log('Using game_id from intervention data:', interventionId);
      } else {
        // Fallback to mapping game type to intervention_id for legacy games
        const gameTypeToInterventionId: Record<string, string> = {
          blend: 'blend-game',
          math: 'math-game',
          memory: 'memory-game',
        };
        interventionId = gameTypeToInterventionId[activeGame?.type || 'blend'] || 'blend-game';
        logger.log('Using mapped intervention ID for legacy game:', interventionId);
      }

      const completeSettings: InterventionSettingsType = {
        gameType: interventionId, // Use the correct intervention_id instead of activeGame.type
        gameTitle: activeGame.title,
        difficultyLevel: settings.difficultyLevel || 'Medium',
        frequency: {
          repeatEvery: settings.frequency?.repeatEvery || '1',
          periodType: settings.frequency?.periodType || 'Week',
          daysOfWeek: settings.frequency?.daysOfWeek || ['M', 'T', 'W', 'T', 'F'],
          timesPerWeek:
            typeof settings.frequency?.timesPerWeek === 'string'
              ? parseInt(settings.frequency.timesPerWeek.toString(), 10)
              : settings.frequency?.timesPerWeek || 2,
          endType: settings.frequency?.endType,
          endDate: settings.frequency?.endDate,
          endTimes:
            typeof settings.frequency?.endTimes === 'string'
              ? parseInt(settings.frequency.endTimes.toString(), 10)
              : settings.frequency?.endTimes,
        },
        duration: settings.duration || 15,
        studentId: effectiveStudentId,
      };

      logger.log('Preparing to schedule intervention with settings:', completeSettings);
      logger.log('Settings frequency:', settings.frequency);
      logger.log('Settings frequency periodType:', settings.frequency?.periodType);

      // Create and assign the intervention (this handles both assignment and scheduling)
      try {
        logger.log(`[${timestamp}] About to call createInterventionFromSettings`);

        const interventionResult = await interventionService.createInterventionFromSettings(
          completeSettings,
          effectiveStudentId
        );

        logger.log(`[${timestamp}] Intervention created successfully:`, interventionResult);
      } catch (createError) {
        logger.error('Error creating intervention:', createError);
        throw createError; // Re-throw to show error to user
      }

      // Note: Assignment is handled by the schedule_intervention API above
      logger.log('Intervention scheduled and ready for student:', effectiveStudentId);

      setIsSettingsModalOpen(false);
      setSuccessMessage(
        `Intervention for ${activeGame.title} has been successfully assigned to the student.`
      );
      setIsSuccessModalOpen(true);
    } catch (error) {
      logger.error('Error assigning intervention:', error);
      toast.error('Failed to assign intervention');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="mt-8">
      <h3 className="text-[20px] font-bold text-black mb-4">Propositions of interventions:</h3>

      {loading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#005773]"></div>
        </div>
      ) : games.length === 0 ? (
        <div className="text-center p-8 text-gray-500">
          No interventions available for this area.
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {games.map(game => (
            <div
              key={game.id}
              className="bg-white rounded-xl shadow-sm overflow-hidden border hover:shadow-md transition-shadow"
            >
              <div className="h-40 bg-gray-200 relative overflow-hidden">
                <img
                  src={game.imageUrl}
                  alt={game.title}
                  className="w-full h-full object-cover"
                  onError={e => {
                    const target = e.target as HTMLImageElement;
                    target.src = 'https://via.placeholder.com/300x200?text=Quiz+Image';
                  }}
                />
                <div className="absolute top-2 left-2 bg-white px-3 py-1 rounded-full text-sm font-medium">
                  Quiz
                </div>
              </div>
              <div className="p-4">
                <h4 className="text-lg font-semibold mb-1">{game.title}</h4>
                <p className="text-gray-600 text-sm mb-3">{game.description}</p>
                <div className="flex items-center text-gray-500 text-sm mb-4">
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  Exercise time: {game.exerciseTime}
                </div>
                <button
                  onClick={() => openGameSettings(game)}
                  className="w-full py-2 bg-[#005773] text-white rounded-lg hover:bg-[#004a63] transition-colors flex items-center justify-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  Settings
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Removed message about the need to select a student */}

      {isSettingsModalOpen && activeGame && (
        <GameSettingsModal
          isOpen={isSettingsModalOpen}
          onClose={() => setIsSettingsModalOpen(false)}
          onSave={handleSaveSettings}
          gameType={activeGame.type as GameType}
          gameTitle={activeGame.title}
          studentId={currentStudentId || getStudentIdFromUrl() || 0} // Use effective student ID
        />
      )}

      <SuccessModal
        isOpen={isSuccessModalOpen}
        onClose={() => setIsSuccessModalOpen(false)}
        onContinue={() => {
          // Continue action handled by modal close
        }}
        message={successMessage}
      />
    </div>
  );
};

export default GameInterventionsComponent;
