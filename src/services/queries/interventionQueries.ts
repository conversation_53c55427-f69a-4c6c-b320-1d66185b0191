import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import interventionService, { InterventionSettings } from '../interventionService';
import logger from '../../utils/logger';

// Mutation hook for creating interventions from settings
export const useCreateInterventionFromSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      settings,
      studentId,
    }: {
      settings: InterventionSettings & {
        frequency: {
          startDate?: string;
          repeatEvery?: string | number;
          timesPerWeek?: string | number;
          daysOfWeek?: string[];
          endType?: 'never' | 'on' | 'after';
          endDate?: string;
          endTimes?: number;
        };
      };
      studentId: number;
    }) => interventionService.createInterventionFromSettings(settings, studentId),
    onSuccess: (_, { studentId }) => {
      logger.log('Intervention created successfully via mutation');
      // Unieważnij zapytania, aby od<PERSON>wi<PERSON><PERSON><PERSON><PERSON> dane - bez wymuszania natychmiastowego pobrania
      queryClient.invalidateQueries({
        queryKey: ['assignedInterventions', studentId],
        exact: false,
      });
    },
    onError: error => {
      logger.error('Error in createInterventionFromSettings mutation:', error);
    },
  });
};

// Hook for fetching assigned interventions with auto-refresh
export const useAssignedInterventions = (studentId?: number) => {
  return useQuery({
    queryKey: ['assignedInterventions', studentId],
    queryFn: async () => {
      if (!studentId) return [];
      try {
        const interventions = await interventionService.getAssignedInterventions(studentId);
        logger.log(`Fetched ${interventions.length} interventions for student ${studentId}`);
        return interventions || [];
      } catch (error) {
        logger.error(`Error fetching interventions for student ${studentId}:`, error);
        return [];
      }
    },
    enabled: !!studentId,
    staleTime: 5 * 1000, // 5 sekund - zapobiega podwójnym żądaniom
    refetchOnWindowFocus: true,
    refetchOnMount: 'always', // Odśwież przy każdym montażu komponentu
    refetchOnReconnect: true, // Odśwież przy ponownym połączeniu
  });
};
