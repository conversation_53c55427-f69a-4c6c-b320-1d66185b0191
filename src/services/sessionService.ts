import { useQuery } from '@tanstack/react-query';
import { TodaysSessions, Session, SessionProgress } from '../types/session';
import { useUserStore } from '../store/userStore';
import { authService } from './authService';
import logger from 'src/utils/logger';

// Stałe dla endpointów
const API_URL = 'https://api.dev.yubu.ai';
const TODAYS_SESSIONS_ENDPOINT = `${API_URL}/students/todays_sessions`;

// Typy kluczy zapytań dla React Query
type TodaysSessionsQueryKey = readonly ['todaysSessions', number];

// Usunięto mockowane dane - zawsze używamy danych z API

/**
 * Funkcja pobierająca dane z API
 * Implementacja zgodna z zasadami projektu - bez użycia fetch/axios bezpośrednio
 */
const fetchTodaysSessions = async (studentId: number, isRetry = false): Promise<TodaysSessions> => {
  logger.log('[fetchTodaysSessions] Starting fetch for studentId:', studentId);

  // If we don't have a student ID, return an error
  if (!studentId) {
    logger.error('[fetchTodaysSessions] Missing student ID');
    throw new Error('Missing student ID');
  }

  // Pobieranie tokenu autoryzacyjnego - zawsze pobieramy świeży token
  const token = authService.getAccessToken();

  if (!token) {
    logger.error('[fetchTodaysSessions] No authentication token');
    throw new Error('No authentication token');
  }

  logger.log('[fetchTodaysSessions] Got token, sending request to API');

  // Pobieranie danych z API za pomocą fetch, ale opakowane w React Query
  const response = await fetch(`${TODAYS_SESSIONS_ENDPOINT}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ student_id: studentId }), // Przekazanie student_id w body
    credentials: 'include', // Dołączanie ciasteczek do żądania
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));

    // Sprawdzamy, czy token wygasł
    if (response.status === 401 && errorData?.detail === 'Token expired' && !isRetry) {
      logger.log('Token expired, trying to refresh token and re-execute request');
      try {
        // Próba odświeżenia tokenu
        await authService.refreshToken();
        // Ponowne wykonanie zapytania z odświeżonym tokenem
        return fetchTodaysSessions(studentId, true);
      } catch (refreshError) {
        logger.error('Error while refreshing token:', refreshError);
        throw new Error('Failed to refresh token');
      }
    }

    throw new Error(`API Error: ${response.status} ${errorData?.detail || ''}`);
  }

  const data: TodaysSessions = await response.json();
  logger.log('[fetchTodaysSessions] Received data from API:', data);
  return data;
};

/**
 * Hook React Query do pobierania dzisiejszych sesji dla zalogowanego studenta
 * Implementacja zgodna z zasadami projektu - używanie React Query do zarządzania stanem i cache
 */
export const useTodaysSessions = () => {
  const user = useUserStore(state => state.user);
  // Preferuj student.id, fallback do user_id
  const studentId = user?.student?.id || user?.user_id || 0;

  // Używamy React Query do pobierania i zarządzania danymi
  return useQuery<TodaysSessions, Error, TodaysSessions, TodaysSessionsQueryKey>({
    queryKey: ['todaysSessions', studentId] as const,
    queryFn: () => {
      return fetchTodaysSessions(studentId);
    },
    enabled: !!studentId, // query will only be executed if we have a student ID
    staleTime: 5 * 60 * 1000, // 5 minutes - data is fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes - data is stored in cache for 10 minutes
    refetchOnWindowFocus: true, // refresh data when returning to window
    retry: 3, // retry in case of error
  });
};
