import { ReadingAreaDetails, ChatStreamPayload } from '../types';
import { chatService } from '../../../services/chatService';

// Stałe dla specjalnych znaczników w odpowiedziach API
export const API_MARKERS = {
  DONE: '[DONE]',
  FULL_RESPONSE: '[FULL_RESPONSE]',
  END: '[[END]]',
};

/**
 * Interfejs dla obiektu gry
 */
export interface GameItem {
  domain?: string;
  exercise?: string;
  description?: string;
  subskill?: string;
  type?: string;
  title?: string;
  exerciseTime?: string;
  imageUrl?: string;
  [key: string]: unknown;
}

/**
 * Interfejs dla interwencji z nowego formatu odpowiedzi
 */
export interface InterventionItem {
  intervention_id: string;
}

/**
 * Interface for new response format with interventions
 */
export interface ChatResponseWithInterventions {
  interventions?: InterventionItem[] | string[]; // Supports both formats: objects and strings
  text?: string;
}

/**
 * Detects array of arrays with intervention_id (new format)
 * @param text Text to search
 * @returns Array of interventions or null if not found
 */
export const detectInterventionArraysInText = (text: string): InterventionItem[] | null => {
  try {
    // Search for array of arrays pattern in text
    const arrayMatch = text.match(/\[\s*\[[\s\S]*?\]\s*\]/);

    if (arrayMatch) {
      try {
        const arrayData = JSON.parse(arrayMatch[0]);

        // Sprawdzamy, czy to tablica tablic stringów
        if (
          Array.isArray(arrayData) &&
          arrayData.length > 0 &&
          arrayData.every(item => Array.isArray(item) && item.every(id => typeof id === 'string'))
        ) {
          // Spłaszczamy tablicę tablic do pojedynczej tablicy intervention_id
          const flattenedIds = arrayData.flat();

          // Konwertujemy na format InterventionItem
          return flattenedIds.map(id => ({ intervention_id: id }));
        }
      } catch (e) {
        // JSON parsing error
      }
    }

    return null;
  } catch (e) {
    return null;
  }
};

/**
 * Detects new response format with interventions and text
 * @param text Text to search
 * @returns Object with interventions and text or null if not found
 */
export const detectInterventionsInText = (text: string): ChatResponseWithInterventions | null => {
  try {
    // First check array of arrays format
    const interventionArrays = detectInterventionArraysInText(text);
    if (interventionArrays && interventionArrays.length > 0) {
      // Extract text after array
      const arrayMatch = text.match(/\[\s*\[[\s\S]*?\]\s*\]/);
      if (arrayMatch) {
        const textAfterArray = text.substring(arrayMatch.index! + arrayMatch[0].length).trim();
        return {
          interventions: interventionArrays,
          text: textAfterArray || '',
        };
      }
    }

    // Sprawdzamy format tablicy obiektów z intervention_id (format z historii czatu)
    const arrayMatch = text.match(/^\s*\[\s*\{[\s\S]*?\}\s*\]/);
    if (arrayMatch) {
      try {
        const arrayData = JSON.parse(arrayMatch[0]);

        // Sprawdzamy, czy to tablica obiektów z intervention_id
        if (
          Array.isArray(arrayData) &&
          arrayData.length > 0 &&
          arrayData.every(
            item =>
              typeof item === 'object' && item !== null && typeof item.intervention_id === 'string'
          )
        ) {
          // Wyodrębniamy tekst po tablicy
          const textAfterArray = text.substring(arrayMatch.index! + arrayMatch[0].length).trim();

          return {
            interventions: arrayData as InterventionItem[],
            text: textAfterArray || '',
          };
        }
      } catch (e) {
        // JSON array parsing error
      }
    }

    // Sprawdzamy format prostej tablicy stringów ["math-game", "memory-game"]
    const stringArrayMatch = text.match(/^\s*\[\s*"[^"]*"[\s\S]*?\]/);
    if (stringArrayMatch) {
      try {
        const arrayData = JSON.parse(stringArrayMatch[0]);

        // Sprawdzamy, czy to tablica stringów
        if (
          Array.isArray(arrayData) &&
          arrayData.length > 0 &&
          arrayData.every(item => typeof item === 'string')
        ) {
          // Konwertujemy na format InterventionItem
          const interventions = arrayData.map(id => ({ intervention_id: id }));

          // Wyodrębniamy tekst po tablicy
          const textAfterArray = text
            .substring(stringArrayMatch.index! + stringArrayMatch[0].length)
            .trim();

          return {
            interventions,
            text: textAfterArray || '',
          };
        }
      } catch (e) {
        // JSON string array parsing error
      }
    }

    // Szukamy wzorca JSON obiektu w tekście - używamy bardziej elastycznego wzorca
    // Szukamy od pierwszego { do ostatniego }
    const startIndex = text.indexOf('{');
    const lastIndex = text.lastIndexOf('}');

    if (startIndex !== -1 && lastIndex !== -1 && lastIndex > startIndex) {
      const jsonString = text.substring(startIndex, lastIndex + 1);

      try {
        const responseObject = JSON.parse(jsonString);

        // Sprawdzamy, czy to obiekt z polami interventions lub text
        if (
          typeof responseObject === 'object' &&
          responseObject !== null &&
          (responseObject.interventions || responseObject.text)
        ) {
          // Konwertujemy tablicę stringów na tablicę InterventionItem jeśli potrzeba
          if (
            Array.isArray(responseObject.interventions) &&
            responseObject.interventions.length > 0 &&
            typeof responseObject.interventions[0] === 'string'
          ) {
            responseObject.interventions = responseObject.interventions.map((id: string) => ({
              intervention_id: id,
            }));
          }
          return responseObject as ChatResponseWithInterventions;
        }
      } catch (e) {
        // JSON parsing error - trying with a simpler pattern
        const simpleMatch = text.match(/\{[^{}]*\}/);
        if (simpleMatch) {
          try {
            const simpleObject = JSON.parse(simpleMatch[0]);
            if (
              typeof simpleObject === 'object' &&
              simpleObject !== null &&
              (simpleObject.interventions || simpleObject.text)
            ) {
              return simpleObject as ChatResponseWithInterventions;
            }
          } catch (e2) {
            // Ignore
          }
        }
      }
    }

    return null;
  } catch (e) {
    return null;
  }
};

/**
 * Detects array of games in text (old function for compatibility)
 * @param text Text to search
 * @returns Array of games or null if not found
 */
export const detectGamesInText = (text: string): GameItem[] | null => {
  try {
    // Search for JSON array pattern in text
    const jsonArrayMatch = text.match(/\[\s*\{[\s\S]*?\}\s*\]/g);

    if (jsonArrayMatch) {
      // Try to parse each match as JSON
      for (const match of jsonArrayMatch) {
        try {
          const gamesArray = JSON.parse(match);

          // Sprawdzamy, czy to tablica obiektów z wymaganymi polami
          if (
            Array.isArray(gamesArray) &&
            gamesArray.length > 0 &&
            gamesArray.some(game => game.domain && game.exercise && game.description)
          ) {
            // Removed console.log
            return gamesArray as GameItem[];
          }
        } catch (e) {
          // Removed console.log
        }
      }
    }

    return null;
  } catch (e) {
    // Removed console.error
    return null;
  }
};

/**
 * Przetwarza chunk odpowiedzi z API i zwraca zaktualizowany tekst odpowiedzi
 * @param chunk Chunk tekstu otrzymany z API
 * @param currentResponseText Aktualny tekst odpowiedzi
 * @returns Obiekt zawierający zaktualizowany tekst odpowiedzi i informację, czy zawartość została zmieniona
 */
export const processResponseChunk = (
  chunk: string,
  currentResponseText: string
): {
  responseText: string;
  hasNewContent: boolean;
  games?: GameItem[];
  interventions?: InterventionItem[];
} => {
  // Sprawdzamy, czy mamy pełną odpowiedź z [FULL_RESPONSE]
  if (chunk.includes('[FULL_RESPONSE]')) {
    // Wyodrębniamy pełną odpowiedź
    const fullResponseMatch = chunk.match(/\[FULL_RESPONSE\]([\s\S]*?)(?:\[\[END\]\]|$)/);
    if (fullResponseMatch && fullResponseMatch[1]) {
      const fullResponseText = fullResponseMatch[1].trim();

      // Najpierw sprawdzamy nowy format z interwencjami
      const detectedInterventions = detectInterventionsInText(fullResponseText);
      if (detectedInterventions) {
        // Konwertujemy interventions na InterventionItem[] jeśli potrzeba
        let interventions: InterventionItem[] | undefined;
        if (detectedInterventions.interventions) {
          if (
            Array.isArray(detectedInterventions.interventions) &&
            detectedInterventions.interventions.length > 0
          ) {
            if (typeof detectedInterventions.interventions[0] === 'string') {
              interventions = (detectedInterventions.interventions as string[]).map(id => ({
                intervention_id: id,
              }));
            } else {
              interventions = detectedInterventions.interventions as InterventionItem[];
            }
          }
        }

        return {
          responseText: detectedInterventions.text || '',
          hasNewContent: true,
          interventions,
        };
      }

      // Sprawdzamy, czy pełna odpowiedź zawiera tablicę gier (stary format)
      const detectedGames = detectGamesInText(fullResponseText);
      if (detectedGames) {
        // Zwracamy tablicę gier
        return {
          responseText: fullResponseText,
          hasNewContent: true,
          games: detectedGames,
        };
      }

      // Jeśli nie znaleziono ani interwencji ani gier, zwracamy pełną odpowiedź jako tekst
      return {
        responseText: fullResponseText,
        hasNewContent: true,
      };
    }
  }

  // Standardowe przetwarzanie chunka
  const lines = chunk.split('\n');
  let responseText = currentResponseText;
  let hasNewContent = false;

  // Sprawdzamy, czy w aktualnym tekście odpowiedzi są interwencje (nowy format)
  const detectedInterventions = detectInterventionsInText(currentResponseText + chunk);
  if (detectedInterventions) {
    // Konwertujemy interventions na InterventionItem[] jeśli potrzeba
    let interventions: InterventionItem[] | undefined;
    if (detectedInterventions.interventions) {
      if (
        Array.isArray(detectedInterventions.interventions) &&
        detectedInterventions.interventions.length > 0
      ) {
        if (typeof detectedInterventions.interventions[0] === 'string') {
          interventions = (detectedInterventions.interventions as string[]).map(id => ({
            intervention_id: id,
          }));
        } else {
          interventions = detectedInterventions.interventions as InterventionItem[];
        }
      }
    }

    return {
      responseText: detectedInterventions.text || '',
      hasNewContent: true,
      interventions,
    };
  }

  // Sprawdzamy, czy w aktualnym tekście odpowiedzi jest tablica gier (stary format)
  const detectedGames = detectGamesInText(currentResponseText + chunk);
  if (detectedGames) {
    return {
      responseText: currentResponseText + chunk,
      hasNewContent: true,
      games: detectedGames,
    };
  }

  for (const line of lines) {
    // Pomijamy puste linie
    if (!line.trim()) continue;

    // Usuwamy prefiks "data:" i białe znaki
    const cleanLine = line.replace(/^data:\s*/i, '').trim();

    // Pomijamy puste linie po wyczyszczeniu i specjalne znaczniki
    if (
      !cleanLine ||
      cleanLine === API_MARKERS.DONE ||
      cleanLine === API_MARKERS.FULL_RESPONSE ||
      cleanLine === API_MARKERS.END
    ) {
      continue;
    }

    // Jeśli to pełna odpowiedź, używamy jej zamiast częściowych wyników
    if (cleanLine.includes('[FULL_RESPONSE]')) {
      const fullResponse = cleanLine.replace(/\[FULL_RESPONSE\]/, '').trim();
      if (fullResponse) {
        responseText = fullResponse;
        hasNewContent = true;
        break; // Przerwij pętlę, mamy pełną odpowiedź
      }
      continue;
    }

    // Dodajemy spację przed nowym słowem, jeśli to konieczne
    if (
      responseText &&
      responseText.length > 0 &&
      !responseText.endsWith(' ') &&
      !cleanLine.startsWith(' ')
    ) {
      responseText += ' ';
    }

    // Dodajemy oczyszczoną linię do odpowiedzi
    responseText += cleanLine;
    hasNewContent = true;
  }

  // Zwracamy zaktualizowany tekst odpowiedzi i informację, czy zawartość została zmieniona
  if (hasNewContent) {
    // Removed console.log
  }

  return { responseText, hasNewContent };
};

export const handleExercisesSubmit = (
  payload: {
    age: number;
    grade: string;
    domains: string[];
    selectedArea: string;
  },
  onSuccess: (details: ReadingAreaDetails) => void,
  _onError: () => void
) => {
  // Usunięto console.log

  // Przygotowujemy dane do wysłania
  const apiPayload: ChatStreamPayload = {
    age: payload.age,
    grade: payload.grade,
    domains: payload.domains,
    question: `What can I do to help them succeed and feel more confident?`,
  };

  let responseText = '';
  let fullResponse = '';

  // Wywołujemy API za pomocą sendChatQuestionFn

  chatService.sendChatQuestion(
    apiPayload,
    (chunk: string) => {
      // Przetwarzamy chunk za pomocą funkcji processResponseChunk
      const { responseText: newResponseText, hasNewContent } = processResponseChunk(
        chunk,
        responseText
      );

      // Aktualizujemy tekst odpowiedzi tylko jeśli zawartość została zmieniona
      if (hasNewContent) {
        responseText = newResponseText;

        // Sprawdzamy, czy chunk zawiera pełną odpowiedź
        if (chunk.includes('[FULL_RESPONSE]')) {
          const fullResponseMatch = chunk.match(/\[FULL_RESPONSE\](.+?)(?=(\[\[END\]\]|$))/s);
          if (fullResponseMatch && fullResponseMatch[1]) {
            fullResponse = fullResponseMatch[1].trim();
          }
        }
      }
    },
    () => {
      // After completion, process the response
      // Removed console.log

      // If full response not found, search for it in the entire response text
      if (!fullResponse) {
        const fullResponseMatch = responseText.match(/\[FULL_RESPONSE\](.+?)(?=(\[\[END\]\]|$))/s);
        if (fullResponseMatch && fullResponseMatch[1]) {
          fullResponse = fullResponseMatch[1].trim();
        } else {
          // Jeśli nadal nie znaleziono, używamy całego tekstu
          fullResponse = responseText.replace(/data:\s/g, '').trim();
        }
      }

      try {
        // Tworzymy szczegóły obszaru Math Problem na podstawie odpowiedzi API
        const mathAreaDetails: ReadingAreaDetails = {
          title: 'Math Problem',
          content: {
            learningObjective:
              'Develop mathematical problem-solving skills appropriate for the selected grade level.',
            exercises: [
              {
                title: 'Math Problem Solving',
                description:
                  fullResponse ||
                  'Practice solving age-appropriate math problems with guided exercises.',
                instructions: 'Select a game below to start practicing math skills.',
                images: [],
              },
            ],
          },
        };

        // Wywołujemy callback z sukcesem
        onSuccess(mathAreaDetails);
      } catch (error) {
        // Removed console.error

        // W przypadku błędu używamy danych domyślnych
        const defaultMathAreaDetails: ReadingAreaDetails = {
          title: 'Math Problem',
          content: {
            learningObjective:
              'Develop mathematical problem-solving skills appropriate for the selected grade level.',
            exercises: [
              {
                title: 'Math Problem Solving',
                description:
                  'Practice solving age-appropriate math problems with guided exercises.',
                instructions: 'Select a game below to start practicing math skills.',
                images: [],
              },
            ],
          },
        };

        onSuccess(defaultMathAreaDetails);
      }
    }
  );
};

export const formatMessage = (text: string): string => {
  let formattedText = text;

  if (
    formattedText.startsWith('{') &&
    (formattedText.endsWith('}') || formattedText.includes('}'))
  ) {
    try {
      const lastBraceIndex = formattedText.lastIndexOf('}');
      const jsonPart = formattedText.substring(0, lastBraceIndex + 1);

      const jsonData = JSON.parse(jsonPart);
      if (jsonData.message) {
        formattedText = jsonData.message;
      } else if (jsonData.answer) {
        formattedText = jsonData.answer;
      } else if (jsonData.response) {
        formattedText = jsonData.response;
      } else if (jsonData.text) {
        formattedText = jsonData.text;
      } else if (jsonData.content) {
        formattedText = jsonData.content;
      }
    } catch (error) {
      // Removed console.log
    }
  }

  let formattedResponse = formattedText
    .replace(/{"[^"]+":\s*"/g, '')
    .replace(/"}$/g, '')
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/#{1,6}\s(.+)/g, '$1')
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    .replace(/\\n/g, '\n')
    .replace(/\\"([^\\"]+)\\"/g, '"$1"')
    .replace(/\\\\/g, '\\')
    .replace(/\\'/g, "'")
    .replace(/^"/g, '')
    .replace(/"$/g, '')
    .trim();

  formattedResponse = formattedResponse
    .replace(/(\d+\.\s)/g, '\n\n$1')
    .replace(/\n{3,}/g, '\n\n')
    .replace(/\n\s+/g, '\n');

  return formattedResponse;
};
