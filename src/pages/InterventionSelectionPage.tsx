import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import GameSettingsModal from '../components/modals/GameSettingsModal';
import interventionService, {
  InterventionSettings,
  getFallbackGamesForArea,
} from '../services/interventionService';
import logger from '../utils/logger';

type SupportArea = {
  id: string;
  name: string;
  description: string;
  icon?: string;
};

import { GameType } from '../services/gameService';

interface Game {
  id: number;
  type: string;
  title: string;
  description: string;
  exerciseTime: string;
  imageUrl: string;
}

const gradeLevels = ['1st grade', '2nd grade', '3rd grade'];

interface Student {
  id: number;
  name: string;
  grade: string;
}

const InterventionSelectionPage: React.FC = () => {
  const [selectedGradeLevel, setSelectedGradeLevel] = useState<string | null>(null);
  const [selectedArea, setSelectedArea] = useState<string | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeGame, setActiveGame] = useState<Game | null>(null);
  const [supportAreas, setSupportAreas] = useState<SupportArea[]>([]);
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingGames, setLoadingGames] = useState<boolean>(false);

  const students: Student[] = [
    { id: 123, name: 'Alex Johnson', grade: '3rd Grade' },
    { id: 124, name: 'Emma Smith', grade: '4th Grade' },
    { id: 125, name: 'Noah Williams', grade: '2nd Grade' },
  ];

  useEffect(() => {
    const fetchSupportAreas = async () => {
      try {
        setLoading(true);
        const areas = await interventionService.getSupportAreas();
        setSupportAreas(areas);
        logger.log('Support areas loaded:', areas);
      } catch (error) {
        logger.error('Error fetching support areas:', error);
        toast.error('Failed to load support areas');
      } finally {
        setLoading(false);
      }
    };

    fetchSupportAreas();
  }, []);

  useEffect(() => {
    if (!selectedArea) {
      setGames([]);
      return;
    }

    const fetchGames = async () => {
      try {
        setLoadingGames(true);
        const gamesForArea = getFallbackGamesForArea(selectedArea);
        setGames(gamesForArea);
      } catch (error) {
        logger.error('Error fetching games:', error);
        toast.error('Failed to load games for selected area');
      } finally {
        setLoadingGames(false);
      }
    };

    fetchGames();
  }, [selectedArea]);

  const handleGradeLevelSelect = (grade: string) => {
    setSelectedGradeLevel(grade);
    setSelectedArea(null);
  };

  const handleAreaSelect = (areaId: string) => {
    setSelectedArea(areaId);
  };

  const openGameSettings = (game: Game) => {
    if (!selectedStudent) {
      toast.error('Please select a student first');
      return;
    }

    setActiveGame(game);
    setIsModalOpen(true);
  };

  // Define a type for the settings received from the modal
  interface GameSettings {
    difficultyLevel?: string;
    frequency?: {
      repeatEvery: string;
      periodType: string;
      timesPerWeek?: number | string;
      daysOfWeek?: string[];
      startDate?: string;
      endType?: 'never' | 'on' | 'after';
      endTimes?: string | number;
      endDate?: string;
    };
    duration?: number;
    details?: Record<string, unknown>;
    promptAi?: {
      customPrompt: string;
    };
  }

  const handleSaveSettings = async (settings: GameSettings) => {
    if (!activeGame || !selectedStudent) return;

    try {
      const completeSettings: InterventionSettings = {
        gameType: activeGame.type,
        gameTitle: activeGame.title,
        difficultyLevel: settings.difficultyLevel || 'Medium',
        frequency: {
          repeatEvery: settings.frequency?.repeatEvery || '1',
          periodType: settings.frequency?.periodType || 'Week',
          daysOfWeek: settings.frequency?.daysOfWeek || ['M', 'T', 'W', 'T', 'F'],
          timesPerWeek:
            typeof settings.frequency?.timesPerWeek === 'string'
              ? parseInt(settings.frequency.timesPerWeek.toString(), 10)
              : settings.frequency?.timesPerWeek || 2,
          endType: settings.frequency?.endType,
          endDate: settings.frequency?.endDate,
          endTimes:
            typeof settings.frequency?.endTimes === 'string'
              ? parseInt(settings.frequency.endTimes.toString(), 10)
              : settings.frequency?.endTimes,
        },
        duration: settings.duration || 15,
        studentId: selectedStudent.id,
      };

      const response = await interventionService.createAndAssignIntervention(
        completeSettings,
        selectedStudent.id
      );

      logger.log('Intervention assigned:', response);
      toast.success(
        `Intervention #${activeGame.id} for ${activeGame.title} assigned to ${selectedStudent.name}`
      );
    } catch (error) {
      logger.error('Error assigning intervention:', error);
      toast.error('Failed to assign intervention');
    }
  };

  const renderGradeLevels = () => (
    <div className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Select Grade Level</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {gradeLevels.map(grade => (
          <button
            key={grade}
            onClick={() => handleGradeLevelSelect(grade)}
            className={`p-4 rounded-lg border-2 transition-colors ${
              selectedGradeLevel === grade
                ? 'border-[#005773] bg-[#005773] bg-opacity-10 text-[#005773]'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            {grade}
          </button>
        ))}
      </div>
    </div>
  );

  const renderSupportAreas = () => (
    <div className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Choose the area of reading you want help with:</h2>
      {loading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#005773]"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {supportAreas.map(area => (
            <button
              key={area.id}
              onClick={() => handleAreaSelect(area.id)}
              className={`p-4 rounded-lg border-2 transition-colors ${
                selectedArea === area.id
                  ? 'border-[#005773] bg-[#005773] bg-opacity-10 text-[#005773]'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="font-medium">{area.name}</div>
            </button>
          ))}
        </div>
      )}
    </div>
  );

  const renderAreaDetails = () => {
    if (!selectedArea) return null;

    const area = supportAreas.find(a => a.id === selectedArea);
    if (!area) return null;

    return (
      <div className="mb-8 bg-white rounded-xl shadow-md p-6">
        <h2 className="text-2xl font-bold mb-2">{area.name}</h2>
        <p className="text-gray-600 mb-6">{area.description}</p>

        <h3 className="text-xl font-semibold mb-4">Propositions of interventions:</h3>

        {loadingGames ? (
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#005773]"></div>
          </div>
        ) : games.length === 0 ? (
          <div className="text-center p-8 text-gray-500">
            No interventions available for this area.
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {games.map(game => (
              <div
                key={game.id}
                className="bg-white rounded-xl shadow-sm overflow-hidden border hover:shadow-md transition-shadow"
              >
                <div className="h-40 bg-gray-200 relative overflow-hidden">
                  <img
                    src={game.imageUrl}
                    alt={game.title}
                    className="w-full h-full object-cover"
                    onError={e => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://via.placeholder.com/300x200?text=Quiz+Image';
                    }}
                  />
                  <div className="absolute top-2 left-2 bg-white px-3 py-1 rounded-full text-sm font-medium">
                    Quiz
                  </div>
                </div>
                <div className="p-4">
                  <h4 className="text-lg font-semibold mb-1">{game.title}</h4>
                  <p className="text-gray-600 text-sm mb-3">{game.description}</p>
                  <div className="flex items-center text-gray-500 text-sm mb-4">
                    <svg
                      className="w-4 h-4 mr-1"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    Exercise time: {game.exerciseTime}
                  </div>
                  <button
                    onClick={() => openGameSettings(game)}
                    className="w-full py-2 bg-[#005773] text-white rounded-lg hover:bg-[#004a63] transition-colors flex items-center justify-center"
                  >
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                    </svg>
                    Settings
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderStudentSelection = () => (
    <div className="mb-8">
      <h2 className="text-xl font-semibold mb-4">Select Student</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {students.map(student => (
          <div
            key={student.id}
            onClick={() => setSelectedStudent(student)}
            className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
              selectedStudent?.id === student.id
                ? 'border-[#005773] bg-[#005773] bg-opacity-10'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="font-medium">{student.name}</div>
            <div className="text-sm text-gray-500">{student.grade}</div>
            <div className="text-sm text-gray-500 font-bold">ID: {student.id}</div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Student Intervention Selection</h1>

        {renderStudentSelection()}

        {renderGradeLevels()}

        {selectedGradeLevel && renderSupportAreas()}
        {selectedArea && renderAreaDetails()}

        {isModalOpen && activeGame && (
          <GameSettingsModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            onSave={handleSaveSettings}
            gameType={activeGame.type as GameType}
            gameTitle={activeGame.title}
            studentId={selectedStudent?.id || 0}
          />
        )}
      </div>
    </div>
  );
};

export default InterventionSelectionPage;
